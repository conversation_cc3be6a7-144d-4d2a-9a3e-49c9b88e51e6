import React, { useRef, useState, useCallback, useEffect } from 'react';
import { SecureIframe } from '../../shared/SecureIframe';
import { createAuthHandler, AUTH_BUTTON_SELECTORS } from './utils/iframeAuthUtils';
import './Analytics.scss';

const AnalyticsWithAuth = ({
  src,
  authButtonSelectors = AUTH_BUTTON_SELECTORS.SUPERSET,
  authTimeout = 15000,
  maxAuthAttempts = 3,
  authCheckInterval = 2000,
  onAuthSuccess,
  onAuthFailure,
  onAuthTimeout,
  className = '',
  ...iframeProps
}) => {
  const iframeRef = useRef(null);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [authAttempts, setAuthAttempts] = useState(0);
  const [authError, setAuthError] = useState(null);
  const authHandlerRef = useRef(null);

  // Initialize auth handler
  useEffect(() => {
    authHandlerRef.current = createAuthHandler({
      selectors: authButtonSelectors,
      maxAttempts: maxAuthAttempts,
      attemptInterval: authCheckInterval,
      timeout: authTimeout
    });

    return () => {
      authHandlerRef.current?.stop();
    };
  }, [authButtonSelectors, maxAuthAttempts, authCheckInterval, authTimeout]);

  // Start authentication process
  const startAuthentication = useCallback(() => {
    if (!authHandlerRef.current || !iframeRef.current) return;

    setIsAuthenticating(true);
    setAuthError(null);
    setAuthAttempts(0);

    authHandlerRef.current.start(iframeRef.current, {
      onSuccess: () => {
        console.log('Authentication successful');
        setIsAuthenticating(false);
        setAuthError(null);
        onAuthSuccess?.();
      },
      onFailure: (error) => {
        console.error('Authentication failed:', error);
        setIsAuthenticating(false);
        setAuthError(error);
        onAuthFailure?.(error);
      },
      onTimeout: () => {
        console.warn('Authentication timeout');
        setIsAuthenticating(false);
        setAuthError('Authentication timeout');
        onAuthTimeout?.();
      },
      onAttempt: (attempt, maxAttempts) => {
        console.log(`Authentication attempt ${attempt}/${maxAttempts}`);
        setAuthAttempts(attempt);
      }
    });
  }, [onAuthSuccess, onAuthFailure, onAuthTimeout]);

  // Handle iframe load
  const handleIframeLoad = useCallback(() => {
    console.log('Iframe loaded, starting authentication check');

    // Small delay to ensure content is fully rendered
    setTimeout(() => {
      startAuthentication();
    }, 1000);
  }, [startAuthentication]);

  // Handle iframe error
  const handleIframeError = useCallback(() => {
    console.error('Iframe failed to load');
    authHandlerRef.current?.stop();
    setIsAuthenticating(false);
    setAuthError('Failed to load iframe');
    onAuthFailure?.('Failed to load iframe');
  }, [onAuthFailure]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      authHandlerRef.current?.stop();
    };
  }, []);

  // Determine loading message
  const getLoadingMessage = () => {
    if (isAuthenticating) {
      return `Authenticating... (Attempt ${authAttempts}/${maxAuthAttempts})`;
    }
    return 'Loading analytics...';
  };

  // Determine if we should show loading
  const shouldShowLoading = isAuthenticating;

  return (
    <div className={`analytics-with-auth ${className}`}>
      {authError && (
        <div className="auth-error-banner" style={{
          background: '#fff3cd',
          border: '1px solid #ffeaa7',
          color: '#856404',
          padding: '8px 12px',
          marginBottom: '8px',
          borderRadius: '4px',
          fontSize: '14px'
        }}>
          Authentication issue: {authError}
        </div>
      )}
      
      <SecureIframe
        ref={iframeRef}
        src={src}
        title="Superset Analytics Panel"
        className={`analytics-iframe ${className}`}
        allowFullScreen
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        showLoadingIndicator={shouldShowLoading}
        loadingMessage={getLoadingMessage()}
        {...iframeProps}
      />
    </div>
  );
};

export default AnalyticsWithAuth;
