/**
 * Utility functions for handling iframe authentication
 */

// Common selectors for authentication buttons across different platforms
export const AUTH_BUTTON_SELECTORS = {
  SUPERSET: [
    'button[type="submit"]',
    '.btn-primary',
    '[data-test="sign-in-button"]',
    'input[type="submit"]',
    '.login-button',
    '#kc-login'
  ],
  KEYCLOAK: [
    '#kc-login',
    'input[name="login"]',
    'input[type="submit"]',
    '.btn-primary'
  ],
  GENERIC: [
    'button[type="submit"]',
    'input[type="submit"]',
    '.btn-primary',
    '.login-button',
    '.sign-in-button'
  ]
};

// Common selectors for detecting authentication pages
export const AUTH_PAGE_INDICATORS = [
  'form[action*="auth"]',
  'form[action*="login"]',
  '.login-form',
  '.auth-form',
  '#kc-form-login',
  '[data-test="login-form"]'
];

/**
 * Check if the iframe content requires authentication
 * @param {HTMLIFrameElement} iframe - The iframe element
 * @param {string[]} indicators - Array of selectors to check for auth pages
 * @returns {boolean} - True if authentication is needed
 */
export const needsAuthentication = (iframe, indicators = AUTH_PAGE_INDICATORS) => {
  if (!iframe) return false;

  try {
    const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;
    if (!iframeDocument) return false;

    // Check for authentication page indicators
    return indicators.some(selector => {
      const element = iframeDocument.querySelector(selector);
      return element && element.offsetParent !== null; // Element exists and is visible
    });
  } catch (error) {
    console.warn('Cannot check authentication status due to cross-origin restrictions');
    return false;
  }
};

/**
 * Find and click authentication button
 * @param {HTMLIFrameElement} iframe - The iframe element
 * @param {string[]} selectors - Array of button selectors to try
 * @returns {boolean} - True if button was found and clicked
 */
export const clickAuthButton = (iframe, selectors = AUTH_BUTTON_SELECTORS.GENERIC) => {
  if (!iframe) return false;

  try {
    const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;
    if (!iframeDocument) return false;

    // Try each selector until we find a visible button
    for (const selector of selectors) {
      const button = iframeDocument.querySelector(selector);
      if (button && button.offsetParent !== null && !button.disabled) {
        console.log(`Clicking auth button with selector: ${selector}`);
        button.click();
        return true;
      }
    }

    console.warn('No clickable auth button found');
    return false;
  } catch (error) {
    console.error('Error clicking auth button:', error.message);
    return false;
  }
};

/**
 * Wait for iframe content to load and stabilize
 * @param {HTMLIFrameElement} iframe - The iframe element
 * @param {number} timeout - Maximum time to wait in milliseconds
 * @returns {Promise<boolean>} - Resolves to true when content is ready
 */
export const waitForIframeReady = (iframe, timeout = 5000) => {
  return new Promise((resolve) => {
    if (!iframe) {
      resolve(false);
      return;
    }

    let attempts = 0;
    const maxAttempts = timeout / 100;

    const checkReady = () => {
      attempts++;
      
      try {
        const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;
        
        if (iframeDocument && iframeDocument.readyState === 'complete') {
          resolve(true);
          return;
        }
      } catch (error) {
        // Cross-origin restriction, assume ready
        resolve(true);
        return;
      }

      if (attempts >= maxAttempts) {
        resolve(false);
        return;
      }

      setTimeout(checkReady, 100);
    };

    checkReady();
  });
};

/**
 * Monitor iframe URL changes (works only for same-origin)
 * @param {HTMLIFrameElement} iframe - The iframe element
 * @param {Function} callback - Called when URL changes
 * @returns {Function} - Cleanup function
 */
export const monitorIframeUrl = (iframe, callback) => {
  if (!iframe) return () => {};

  let lastUrl = '';
  
  const checkUrl = () => {
    try {
      const currentUrl = iframe.contentWindow?.location?.href;
      if (currentUrl && currentUrl !== lastUrl) {
        lastUrl = currentUrl;
        callback(currentUrl);
      }
    } catch (error) {
      // Cross-origin restriction, ignore
    }
  };

  const interval = setInterval(checkUrl, 1000);
  
  return () => clearInterval(interval);
};

/**
 * Setup postMessage listener for iframe communication
 * @param {Function} messageHandler - Function to handle received messages
 * @returns {Function} - Cleanup function
 */
export const setupPostMessageListener = (messageHandler) => {
  const handleMessage = (event) => {
    // Add origin validation here if needed
    messageHandler(event.data, event.origin);
  };

  window.addEventListener('message', handleMessage);
  
  return () => window.removeEventListener('message', handleMessage);
};

/**
 * Send message to iframe
 * @param {HTMLIFrameElement} iframe - The iframe element
 * @param {any} message - Message to send
 * @param {string} targetOrigin - Target origin for security
 */
export const sendMessageToIframe = (iframe, message, targetOrigin = '*') => {
  if (!iframe || !iframe.contentWindow) return;

  try {
    iframe.contentWindow.postMessage(message, targetOrigin);
  } catch (error) {
    console.error('Error sending message to iframe:', error.message);
  }
};

/**
 * Create a comprehensive authentication handler
 * @param {Object} options - Configuration options
 * @returns {Object} - Authentication handler with methods
 */
export const createAuthHandler = (options = {}) => {
  const {
    selectors = AUTH_BUTTON_SELECTORS.GENERIC,
    indicators = AUTH_PAGE_INDICATORS,
    maxAttempts = 3,
    attemptInterval = 2000,
    timeout = 15000
  } = options;

  let attempts = 0;
  let isRunning = false;
  let timeoutId = null;
  let intervalId = null;

  const cleanup = () => {
    if (timeoutId) clearTimeout(timeoutId);
    if (intervalId) clearInterval(intervalId);
    isRunning = false;
  };

  const start = (iframe, callbacks = {}) => {
    if (isRunning) return;
    
    isRunning = true;
    attempts = 0;

    const { onSuccess, onFailure, onTimeout, onAttempt } = callbacks;

    // Set overall timeout
    timeoutId = setTimeout(() => {
      cleanup();
      onTimeout?.();
    }, timeout);

    // Start authentication attempts
    const attemptAuth = () => {
      if (!isRunning) return;

      attempts++;
      onAttempt?.(attempts, maxAttempts);

      if (needsAuthentication(iframe, indicators)) {
        const success = clickAuthButton(iframe, selectors);
        
        if (success) {
          // Wait a bit and check if auth was successful
          setTimeout(() => {
            if (!needsAuthentication(iframe, indicators)) {
              cleanup();
              onSuccess?.();
            } else if (attempts >= maxAttempts) {
              cleanup();
              onFailure?.('Max attempts reached');
            }
          }, 2000);
        } else if (attempts >= maxAttempts) {
          cleanup();
          onFailure?.('Could not find auth button');
        }
      } else {
        // No auth needed
        cleanup();
        onSuccess?.();
      }
    };

    // Start immediately and then at intervals
    attemptAuth();
    intervalId = setInterval(attemptAuth, attemptInterval);
  };

  return {
    start,
    stop: cleanup,
    isRunning: () => isRunning,
    getAttempts: () => attempts
  };
};
