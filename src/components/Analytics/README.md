# Analytics with Authentication

This component provides automatic authentication handling for embedded analytics iframes, specifically designed for Superset with Keycloak authentication.

## Problem Solved

When embedding Superset analytics in an iframe with Keycloak authentication, users often see a "Sign In" button on first load even when they're already authenticated in the main application. This component automatically detects and clicks that button to provide a seamless user experience.

## Components

### AnalyticsWithAuth

A wrapper around SecureIframe that handles automatic authentication for analytics dashboards.

```jsx
import AnalyticsWithAuth from './AnalyticsWithAuth';

<AnalyticsWithAuth
  src="https://your-superset-url.com"
  authTimeout={15000}
  maxAuthAttempts={3}
  onAuthSuccess={() => console.log('Auth successful')}
  onAuthFailure={(error) => console.error('Auth failed:', error)}
/>
```

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `src` | `string` | **required** | URL of the analytics dashboard |
| `authButtonSelectors` | `string[]` | Superset selectors | Array of CSS selectors for auth buttons |
| `authTimeout` | `number` | `15000` | Total timeout for authentication (ms) |
| `maxAuthAttempts` | `number` | `3` | Maximum number of authentication attempts |
| `authCheckInterval` | `number` | `2000` | Interval between auth attempts (ms) |
| `onAuthSuccess` | `function` | `undefined` | Called when authentication succeeds |
| `onAuthFailure` | `function` | `undefined` | Called when authentication fails |
| `onAuthTimeout` | `function` | `undefined` | Called when authentication times out |
| `className` | `string` | `''` | CSS class for styling |

All other props are passed through to the underlying SecureIframe component.

## How It Works

1. **Iframe Loads**: The component loads the analytics URL in a secure iframe
2. **Authentication Detection**: Checks for authentication forms/buttons using predefined selectors
3. **Automatic Sign-in**: If auth is needed, automatically clicks the sign-in button
4. **Retry Logic**: Retries up to `maxAuthAttempts` times with configurable intervals
5. **Fallback**: If authentication fails, shows the iframe anyway with an error banner

## Authentication Flow

```mermaid
graph TD
    A[Iframe Loads] --> B[Check for Auth Elements]
    B --> C{Auth Needed?}
    C -->|No| D[Show Dashboard]
    C -->|Yes| E[Click Auth Button]
    E --> F[Wait 2s]
    F --> G[Check Again]
    G --> H{Still Need Auth?}
    H -->|No| D
    H -->|Yes| I{Max Attempts?}
    I -->|No| E
    I -->|Yes| J[Show Error Banner]
    J --> K[Show Dashboard Anyway]
```

## Supported Platforms

### Superset (Default)
- `button[type="submit"]`
- `.btn-primary`
- `[data-test="sign-in-button"]`
- `input[type="submit"]`
- `.login-button`
- `#kc-login`

### Keycloak
- `#kc-login`
- `input[name="login"]`
- `input[type="submit"]`
- `.btn-primary`

### Generic
- `button[type="submit"]`
- `input[type="submit"]`
- `.btn-primary`
- `.login-button`
- `.sign-in-button`

## Customization

### Custom Button Selectors

```jsx
import { AUTH_BUTTON_SELECTORS } from './utils/iframeAuthUtils';

<AnalyticsWithAuth
  src="https://your-dashboard.com"
  authButtonSelectors={[
    '#custom-login-btn',
    '.my-auth-button',
    ...AUTH_BUTTON_SELECTORS.GENERIC
  ]}
/>
```

### Custom Authentication Logic

```jsx
import { createAuthHandler } from './utils/iframeAuthUtils';

const customAuthHandler = createAuthHandler({
  selectors: ['#my-button'],
  maxAttempts: 5,
  attemptInterval: 1000,
  timeout: 30000
});
```

## Security Considerations

⚠️ **Important**: This component can only access iframe content from the same origin due to browser security policies (CORS). For cross-origin iframes:

- Authentication detection may not work
- Button clicking will fail silently
- The component will fall back to showing the iframe normally

### Same-Origin Requirements

For full functionality, ensure:
1. Your app and analytics dashboard are on the same domain, OR
2. The analytics dashboard is configured to allow iframe access, OR
3. Use a proxy to serve the dashboard from the same origin

## Troubleshooting

### Authentication Not Working

1. **Check Browser Console**: Look for CORS errors or selector warnings
2. **Verify Selectors**: Inspect the iframe content to find correct button selectors
3. **Test Same-Origin**: Try with a same-origin URL first
4. **Increase Timeout**: Some dashboards take longer to load

### Common Issues

```jsx
// Issue: Button selector not found
// Solution: Add more specific selectors
authButtonSelectors={[
  '#kc-login',
  'button[name="login"]',
  '.btn.btn-primary',
  'input[value="Sign In"]'
]}

// Issue: Authentication too slow
// Solution: Increase timeout and attempts
authTimeout={30000}
maxAuthAttempts={5}
authCheckInterval={3000}
```

## Example Usage

```jsx
import React from 'react';
import AnalyticsWithAuth from './AnalyticsWithAuth';

const MyAnalytics = () => {
  const handleAuthSuccess = () => {
    console.log('User authenticated successfully');
    // Track analytics event, hide loading spinner, etc.
  };

  const handleAuthFailure = (error) => {
    console.error('Authentication failed:', error);
    // Show user-friendly error message
  };

  return (
    <div className="analytics-container">
      <AnalyticsWithAuth
        src="https://superset.example.com/dashboard/1"
        authTimeout={20000}
        maxAuthAttempts={3}
        onAuthSuccess={handleAuthSuccess}
        onAuthFailure={handleAuthFailure}
        className="my-analytics-iframe"
      />
    </div>
  );
};

export default MyAnalytics;
```

## Migration from Basic Analytics

**Before:**
```jsx
<iframe
  src={analyticsUrl}
  className="analyticsIframe"
  title="Analytics"
  allowFullScreen
/>
```

**After:**
```jsx
<AnalyticsWithAuth
  src={analyticsUrl}
  className="analyticsIframe"
  onAuthSuccess={() => console.log('Ready!')}
/>
```
