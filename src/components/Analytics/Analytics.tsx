import React, { FC, useCallback } from 'react';
import { useAppContext } from 'AppContextProvider';
import AnalyticsWithAuth from './AnalyticsWithAuth';
import './Analytics.scss';

const Analytics: FC = () => {
  const { appConfig } = useAppContext();
  const analyticsUrl = (appConfig?.analyticsUrl as string);

  const handleAuthSuccess = useCallback(() => {
    console.log('Analytics authentication successful');
  }, []);

  const handleAuthFailure = useCallback((error: string) => {
    console.error('Analytics authentication failed:', error);
  }, []);

  const handleAuthTimeout = useCallback(() => {
    console.warn('Analytics authentication timed out');
  }, []);

  return (
    <div className="analyticsContainer" data-testid="analytics-container">
      <AnalyticsWithAuth
        src={analyticsUrl}
        className="analyticsIframe"
        authTimeout={15000}
        maxAuthAttempts={3}
        onAuthSuccess={handleAuthSuccess}
        onAuthFailure={handleAuthFailure}
        onAuthTimeout={handleAuthTimeout}
      />
    </div>
  );
};

export default Analytics;
