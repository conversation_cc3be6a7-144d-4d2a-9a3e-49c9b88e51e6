import React, { useState } from 'react';
import AnalyticsWithAuth from './AnalyticsWithAuth';
import { AUTH_BUTTON_SELECTORS } from './utils/iframeAuthUtils';

/**
 * Example component showing different ways to use AnalyticsWithAuth
 */
const AnalyticsExample = () => {
  const [authStatus, setAuthStatus] = useState('idle');
  const [authAttempts, setAuthAttempts] = useState(0);
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { timestamp, message, type }]);
  };

  const handleAuthSuccess = () => {
    setAuthStatus('success');
    addLog('Authentication successful! Dashboard is ready.', 'success');
  };

  const handleAuthFailure = (error) => {
    setAuthStatus('failed');
    addLog(`Authentication failed: ${error}`, 'error');
  };

  const handleAuthTimeout = () => {
    setAuthStatus('timeout');
    addLog('Authentication timed out. Dashboard may still work.', 'warning');
  };

  const clearLogs = () => {
    setLogs([]);
    setAuthStatus('idle');
    setAuthAttempts(0);
  };

  // Example analytics URLs (replace with your actual URLs)
  const exampleUrls = {
    superset: 'https://superset.example.com/dashboard/1',
    local: 'http://localhost:8088/dashboard/1',
    demo: 'https://demo-superset.com/dashboard/public'
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Analytics with Authentication - Examples</h1>
      
      {/* Status Display */}
      <div style={{ 
        marginBottom: '20px', 
        padding: '10px', 
        borderRadius: '4px',
        backgroundColor: 
          authStatus === 'success' ? '#d4edda' :
          authStatus === 'failed' ? '#f8d7da' :
          authStatus === 'timeout' ? '#fff3cd' : '#e2e3e5'
      }}>
        <strong>Status:</strong> {authStatus} 
        {authAttempts > 0 && ` (${authAttempts} attempts)`}
      </div>

      {/* Controls */}
      <div style={{ marginBottom: '20px' }}>
        <button onClick={clearLogs} style={{ marginRight: '10px' }}>
          Clear Logs
        </button>
      </div>

      {/* Example 1: Basic Usage */}
      <div style={{ marginBottom: '30px' }}>
        <h2>Example 1: Basic Superset Dashboard</h2>
        <div style={{ border: '1px solid #ccc', height: '400px' }}>
          <AnalyticsWithAuth
            src={exampleUrls.superset}
            onAuthSuccess={handleAuthSuccess}
            onAuthFailure={handleAuthFailure}
            onAuthTimeout={handleAuthTimeout}
          />
        </div>
      </div>

      {/* Example 2: Custom Configuration */}
      <div style={{ marginBottom: '30px' }}>
        <h2>Example 2: Custom Authentication Settings</h2>
        <div style={{ border: '1px solid #ccc', height: '400px' }}>
          <AnalyticsWithAuth
            src={exampleUrls.local}
            authButtonSelectors={[
              '#kc-login',
              'button[name="login"]',
              ...AUTH_BUTTON_SELECTORS.SUPERSET
            ]}
            authTimeout={30000}
            maxAuthAttempts={5}
            authCheckInterval={1500}
            onAuthSuccess={() => addLog('Custom auth successful!', 'success')}
            onAuthFailure={(error) => addLog(`Custom auth failed: ${error}`, 'error')}
            onAuthTimeout={() => addLog('Custom auth timeout', 'warning')}
          />
        </div>
      </div>

      {/* Example 3: With Error Handling */}
      <div style={{ marginBottom: '30px' }}>
        <h2>Example 3: Demo Dashboard (May Not Work - CORS)</h2>
        <p style={{ color: '#666', fontSize: '14px' }}>
          This example demonstrates what happens when cross-origin restrictions prevent authentication.
        </p>
        <div style={{ border: '1px solid #ccc', height: '400px' }}>
          <AnalyticsWithAuth
            src={exampleUrls.demo}
            authTimeout={10000}
            maxAuthAttempts={2}
            onAuthSuccess={() => addLog('Demo auth successful!', 'success')}
            onAuthFailure={(error) => addLog(`Demo auth failed: ${error}`, 'error')}
            onAuthTimeout={() => addLog('Demo auth timeout - this is expected for cross-origin', 'warning')}
          />
        </div>
      </div>

      {/* Logs Display */}
      <div style={{ marginTop: '30px' }}>
        <h2>Authentication Logs</h2>
        <div style={{ 
          border: '1px solid #ccc', 
          height: '200px', 
          overflow: 'auto', 
          padding: '10px',
          backgroundColor: '#f8f9fa',
          fontFamily: 'monospace',
          fontSize: '12px'
        }}>
          {logs.length === 0 ? (
            <div style={{ color: '#666' }}>No logs yet. Load a dashboard to see authentication attempts.</div>
          ) : (
            logs.map((log, index) => (
              <div 
                key={index} 
                style={{ 
                  marginBottom: '5px',
                  color: 
                    log.type === 'success' ? '#28a745' :
                    log.type === 'error' ? '#dc3545' :
                    log.type === 'warning' ? '#ffc107' : '#333'
                }}
              >
                <span style={{ color: '#666' }}>[{log.timestamp}]</span> {log.message}
              </div>
            ))
          )}
        </div>
      </div>

      {/* Configuration Tips */}
      <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#e9ecef', borderRadius: '4px' }}>
        <h3>Configuration Tips</h3>
        <ul style={{ margin: 0, paddingLeft: '20px' }}>
          <li><strong>Same-Origin:</strong> For best results, serve your analytics from the same domain</li>
          <li><strong>Button Selectors:</strong> Inspect your login page to find the correct button selectors</li>
          <li><strong>Timeouts:</strong> Increase timeout for slower-loading dashboards</li>
          <li><strong>CORS:</strong> Cross-origin iframes may not allow authentication automation</li>
          <li><strong>Testing:</strong> Use browser dev tools to debug authentication issues</li>
        </ul>
      </div>

      {/* Code Examples */}
      <div style={{ marginTop: '30px' }}>
        <h3>Code Example</h3>
        <pre style={{ 
          backgroundColor: '#f8f9fa', 
          padding: '15px', 
          borderRadius: '4px',
          overflow: 'auto',
          fontSize: '12px'
        }}>
{`import AnalyticsWithAuth from './AnalyticsWithAuth';

<AnalyticsWithAuth
  src="https://your-superset.com/dashboard/1"
  authTimeout={15000}
  maxAuthAttempts={3}
  onAuthSuccess={() => console.log('Ready!')}
  onAuthFailure={(error) => console.error(error)}
/>`}
        </pre>
      </div>
    </div>
  );
};

export default AnalyticsExample;
