import React, { useCallback, useState, useRef, useEffect } from 'react';
import './SecureIframe.scss';

const SecureIframe = ({
  src,
  title,
  className = '',
  style = {},
  width = '100%',
  height = '100%',
  allowFullScreen = false,
  sandbox = 'allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation allow-downloads',
  loading = 'lazy',
  onLoad,
  onError,
  showLoadingIndicator = true,
  loadingMessage = 'Loading...',
  showErrorMessage = true,
  errorMessage = 'Failed to load content. Please try again.',
  testId = 'secure-iframe',
  // New authentication-related props
  enableAuthHandler = false,
  authButtonSelector = null,
  authTimeout = 10000,
  authLoadingMessage = 'Authenticating...',
  onAuthStart,
  onAuthComplete,
  onAuthTimeout,
  onAuthError,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const iframeRef = useRef(null);
  const authTimeoutRef = useRef(null);

  // Clear authentication timeout
  const clearAuthTimeout = useCallback(() => {
    if (authTimeoutRef.current) {
      clearTimeout(authTimeoutRef.current);
      authTimeoutRef.current = null;
    }
  }, []);

  // Handle authentication process
  const handleAuthentication = useCallback(async () => {
    if (!enableAuthHandler || !authButtonSelector || !iframeRef.current) {
      return;
    }

    setIsAuthenticating(true);
    onAuthStart?.();

    // Set timeout for authentication
    authTimeoutRef.current = setTimeout(() => {
      setIsAuthenticating(false);
      onAuthTimeout?.();
      console.warn('Authentication timeout reached');
    }, authTimeout);

    try {
      // Try to access iframe content and find the auth button
      const iframeDocument = iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document;

      if (iframeDocument) {
        // Look for the authentication button
        const authButton = iframeDocument.querySelector(authButtonSelector);

        if (authButton) {
          // Click the authentication button
          authButton.click();
          clearAuthTimeout();
          setIsAuthenticating(false);
          onAuthComplete?.();
          console.log('Authentication button clicked successfully');
        } else {
          // Button not found, keep the iframe visible but log the issue
          clearAuthTimeout();
          setIsAuthenticating(false);
          console.warn(`Authentication button not found with selector: ${authButtonSelector}`);
        }
      } else {
        // Cannot access iframe content due to CORS
        clearAuthTimeout();
        setIsAuthenticating(false);
        onAuthError?.('Cannot access iframe content due to cross-origin restrictions');
        console.warn('Cannot access iframe content due to cross-origin restrictions');
      }
    } catch (error) {
      clearAuthTimeout();
      setIsAuthenticating(false);
      onAuthError?.(error.message);
      console.error('Authentication error:', error);
    }
  }, [enableAuthHandler, authButtonSelector, authTimeout, onAuthStart, onAuthComplete, onAuthTimeout, onAuthError, clearAuthTimeout]);

  const handleLoad = useCallback(() => {
    setIsLoading(false);
    setHasError(false);

    // Start authentication process if enabled
    if (enableAuthHandler) {
      // Small delay to ensure iframe content is fully loaded
      setTimeout(() => {
        handleAuthentication();
      }, 500);
    }

    onLoad?.();
  }, [onLoad, enableAuthHandler, handleAuthentication]);

  const handleError = useCallback(() => {
    setIsLoading(false);
    setHasError(true);
    clearAuthTimeout();
    setIsAuthenticating(false);
    onError?.();
  }, [onError, clearAuthTimeout]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      clearAuthTimeout();
    };
  }, [clearAuthTimeout]);

  const iframeStyle = {
    border: 'none',
    width,
    height,
    ...style
  };

  return (
    <div 
      className={`secure-iframe-container ${className}`}
      data-testid={`${testId}-container`}
    >
      {isLoading && showLoadingIndicator && (
        <div className="secure-iframe-loading" data-testid={`${testId}-loading`}>
          <div className="loading-spinner"></div>
          <span className="loading-message">{loadingMessage}</span>
        </div>
      )}
      
      {hasError && showErrorMessage && (
        <div className="secure-iframe-error" data-testid={`${testId}-error`}>
          <div className="error-icon">⚠️</div>
          <span className="error-message">{errorMessage}</span>
        </div>
      )}
      
      <iframe
        src={src}
        title={title}
        className={`secure-iframe ${isLoading ? 'loading' : ''} ${hasError ? 'error' : ''}`}
        style={iframeStyle}
        sandbox={sandbox}
        allowFullScreen={allowFullScreen}
        loading={loading}
        onLoad={handleLoad}
        onError={handleError}
        data-testid={testId}
      />
    </div>
  );
};

export default SecureIframe;
