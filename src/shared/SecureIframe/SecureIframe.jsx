import React, { useState, useRef, forwardRef, useCallback } from 'react';
import './SecureIframe.scss';

const DEFAULT_SANDBOX = 'allow-same-origin allow-scripts allow-forms allow-popups';

const SecureIframe = forwardRef(({
  src,
  title,
  className = '',
  style = {},
  showLoading = true,
  showError = true,
  onLoad,
  onError,
  sandbox = DEFAULT_SANDBOX,
  ...rest
}, ref) => {
  const [status, setStatus] = useState('loading');
  const iframeRef = useRef(null);

  const setIframeRef = useCallback((node) => {
    iframeRef.current = node;
    if (typeof ref === 'function') ref(node);
    else if (ref) ref.current = node;
  }, [ref]);

  const handleLoad = () => {
    setStatus('loaded');
    onLoad?.();
  };

  const handleError = () => {
    setStatus('error');
    onError?.();
  };

  return (
    <div className={`secure-iframe-container ${className}`}>
      {status === 'loading' && showLoading && (
        <div className="secure-iframe-loading">
          <div className="loading-spinner" />
          <span>Loading...</span>
        </div>
      )}

      {status === 'error' && showError && (
        <div className="secure-iframe-error">
          <span>⚠️ Failed to load content.</span>
        </div>
      )}

      <iframe
        ref={setIframeRef}
        src={src}
        title={title}
        className={`secure-iframe ${status}`}
        style={{ width: '100%', height: '100%', border: 'none', ...style }}
        sandbox={sandbox}
        loading="lazy"
        allowFullScreen
        onLoad={handleLoad}
        onError={handleError}
        {...rest}
      />
    </div>
  );
});

export default SecureIframe;
